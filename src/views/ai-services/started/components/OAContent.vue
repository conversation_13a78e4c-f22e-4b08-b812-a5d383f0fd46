<template>
  <div class="oa-content">
    <el-button type="primary" link style="margin-bottom: 10px" @click="onGetPageData">
      点击刷新数据
    </el-button>
    <el-tabs v-model="activeName" v-loading="loading" type="card">
      <el-tab-pane label="流程" name="process">
        <el-timeline style="width: 100%; max-height: 600px; overflow-y: auto; padding: 0 10px">
          <el-timeline-item
            v-for="(item, index) in allProcess"
            :key="index"
            :timestamp="item.CreatedTime"
            placement="top"
            color="green"
          >
            {{ item.Message }}
          </el-timeline-item>
        </el-timeline>
      </el-tab-pane>
      <el-tab-pane label="JSON数据" name="json">
        <VueJsonPretty :data="jsonData" showLineNumber showIcon virtual />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import AIPowered_Api from "@/api/aipowered";
import { FlowInstanceItem } from "@/api/aipowered/types";
import dayjs from "dayjs";
import VueJsonPretty from "vue-json-pretty";
import "vue-json-pretty/lib/styles.css";
const activeName = ref("process");
const jsonData = ref({});
const allProcess = ref<FlowInstanceItem[]>([]);
const loading = ref<boolean>(false);

const props = defineProps<{
  prescriptionId: string;
}>();

const onGetJsonData = async () => {
  const res = await AIPowered_Api.getFlowState({ prescriptionId: props.prescriptionId });
  if (res.Type === 200) {
    jsonData.value = res.Data || {};
  }
};

const onGetAllProcess = async () => {
  const res = await AIPowered_Api.getFlowInstanceList({
    PrescriptionId: props.prescriptionId,
    PageInput: { PageIndex: 1, PageSize: 10000 },
  });
  console.log(res);
  if (res.Type === 0) {
    res.Data.Rows.forEach((item) => {
      item.CreatedTime = dayjs(item.CreatedTime).format("YYYY-MM-DD HH:mm:ss");
    });
    allProcess.value = res.Data.Rows;
  }
};

const onGetPageData = async () => {
  loading.value = true;
  try {
    // 获取json数据
    await onGetJsonData();
    // 获取所有流程图
    await onGetAllProcess();
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.prescriptionId,
  (newVal) => {
    if (newVal) {
      onGetPageData();
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.oa-content {
  width: 100%;
  min-height: 600px;
  overflow-y: auto;
}
</style>
