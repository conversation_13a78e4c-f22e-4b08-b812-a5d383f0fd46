<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="执行日期">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="开方医院">
                <HospitalSelect v-model="queryParams.OrgId" />
              </el-form-item>
              <el-form-item label="治疗项目">
                <el-select
                  v-model="queryParams.BaseMoItemId"
                  :disabled="!queryParams.OrgId"
                  placeholder="请选择"
                  :empty-values="['', undefined, null]"
                  :value-on-clear="() => null"
                  clearable
                >
                  <el-option
                    v-for="item in treatmentList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="执行医院">
                <HospitalSelect v-model="queryParams.ExecOrgId" :is-treatment="true" />
              </el-form-item>
              <el-form-item label="执行人">
                <UserSelect
                  v-model="queryParams.DctId"
                  :role-types="['doctor', 'therapist', 'nurse']"
                />
              </el-form-item>
              <el-form-item label="方案编号" prop="PreId">
                <el-input
                  v-model="queryParams.PreId"
                  placeholder="方案编号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column
            prop="MoItemName"
            label="治疗项目"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column prop="PreId" label="方案编号" show-overflow-tooltip align="center" />
          <el-table-column label="患者信息" align="center">
            <template #default="scope">
              {{ scope.row.PatientName + "  " + scope.row.Sex + " " + scope.row.Age + "岁" }}
            </template>
          </el-table-column>
          <el-table-column label="开方医院" prop="PreOrgName" align="center" />
          <el-table-column label="开方医生" width="80" prop="PreDoctorName" align="center" />
          <el-table-column label="治疗执行机构" prop="OrgName" align="center" />
          <el-table-column
            label="治疗执行时间"
            prop="CreatedTime"
            align="center"
            :formatter="tableDateFormat"
          />
          <el-table-column label="治疗执行人" width="120" prop="DoctorName" align="center" />
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import Content_Api from "@/api/content";
import { MoItemPageData } from "@/api/content/types";
import Community_Api from "@/api/community";
import { ExecuteRecord, GetExecRecordInputDTO } from "@/api/community/types";

defineOptions({
  name: "OfflineExecutionRecord",
});

const treatmentList = ref<MoItemPageData[]>([]);

const queryParams = ref<GetExecRecordInputDTO>({
  State: 1,
  BaseMoItemId: null,
  DctId: null,
  OrgId: null,
  ExecOrgId: null,
  PreId: null,
  StarDate: dayjs(new Date()).format("YYYY-MM-01") + " 00:00:00",
  EndDate: dayjs(new Date()).format("YYYY-MM-DD") + " 23:59:59",
  PageIndex: 1,
  PageSize: 10,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<ExecuteRecord>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const onGetTreatmentList = async (orgId: string) => {
  const res = await Content_Api.getMoItemPageData({
    currentOrganizationId: orgId,
    page: 1,
    pageSize: 1000,
    isEnable: true,
    moItemUseScope: [2],
  });
  treatmentList.value = res.Data.Data;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Community_Api.getExecuteRecord(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
    tableLoading.value = false;
  }
};

watch(timeRange, (newVal) => {
  queryParams.value.StarDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});
watch(
  () => queryParams.value.OrgId,
  (newVal) => {
    if (newVal) {
      queryParams.value.BaseMoItemId = null;
      onGetTreatmentList(newVal);
    } else {
      treatmentList.value = [];
    }
  }
);

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
