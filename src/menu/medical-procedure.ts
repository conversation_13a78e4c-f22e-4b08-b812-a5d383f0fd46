import { type RouteVO } from "@/api/system/menu";

const medicalProcedureRoutes: RouteVO[] = [
  {
    path: "/medical-procedure",
    component: "Layout",
    name: "MedicalProcedure",
    meta: {
      title: "医疗过程管理",
      icon: "system",
      hidden: false,
      alwaysShow: false,
      roles: [
        "scienceTechnologyExhibition",
        "sales",
        "internetHospitalAdmin",
        "assistant",
        "finance",
        "operations",
        "storage",
        "promoter",
        "superOperate",
        "superAdmin",
        "externalSeller",
      ],
    },
    children: [
      {
        path: "medicalRecordInquiry",
        component: "medical-procedure/medicalRecordInquiry/index",
        name: "MedicalRecordInquiry",
        meta: {
          title: "就诊记录查询",
          icon: "el-icon-EditPen",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: [
            "sales",
            "internetHospitalAdmin",
            "assistant",
            "finance",
            "operations",
            "storage",
            "externalSeller",
          ],
        },
      },
      {
        path: "treatmentSchemeQuery",
        component: "medical-procedure/treatmentSchemeQuery/index",
        name: "TreatmentSchemeQuery",
        meta: {
          title: "治疗方案查询",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: [
            "scienceTechnologyExhibition",
            "sales",
            "internetHospitalAdmin",
            "assistant",
            "finance",
            "operations",
            "storage",
            "scienceTechnologyExhibition",
            "superAdmin",
            "externalSeller",
          ],
        },
      },
      {
        path: "rehabilitationPlanInquiry",
        component: "medical-procedure/rehabilitationPlanInquiry/index",
        name: "RehabilitationPlanInquiry",
        meta: {
          title: "康复计划查询",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: [
            "sales",
            "internetHospitalAdmin",
            "assistant",
            "finance",
            "operations",
            "storage",
            "promoter",
            "externalSeller",
          ],
        },
      },
      {
        path: "homeRehabilitationVisit",
        component: "medical-procedure/homeRehabilitationVisit/index",
        name: "HomeRehabilitationVisit",
        meta: {
          title: "居家康复回访",
          icon: "el-icon-Timer",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["operations", "superOperate", "assistant", "scienceTechnologyExhibition"],
        },
      },
      {
        path: "homeRehabilitationVisitNew",
        component: "medical-procedure/homeRehabilitationVisitNew/index",
        name: "HomeRehabilitationVisitNew",
        meta: {
          title: "居家康复回访（新）",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: [
            "operations",
            "superOperate",
            "assistant",
            "scienceTechnologyExhibition",
            "internetHospitalAdmin",
          ],
        },
      },
      {
        path: "followUpManagement",
        component: "medical-procedure/followUpManagement/index",
        name: "FollowUpManagement",
        meta: {
          title: "随访管理",
          icon: "el-icon-Stopwatch",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "operations"],
        },
      },
      {
        path: "followUpResult",
        component: "medical-procedure/followUpResult/index",
        name: "FollowUpResult",
        meta: {
          title: "随访结果",
          icon: "el-icon-DocumentChecked",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "operations"],
        },
      },
      {
        path: "offlineExecutionRecord",
        component: "medical-procedure/offlineExecutionRecord/index",
        name: "OfflineExecutionRecord",
        meta: {
          title: "线下执行记录",
          icon: "el-icon-DocumentChecked",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superAdmin"],
        },
      },
    ],
  },
];
export default medicalProcedureRoutes;
