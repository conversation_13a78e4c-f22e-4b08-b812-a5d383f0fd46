import { type EpPropMergeTypeWithNull } from "element-plus";

export interface FlowListInputDTO {
  PrescriptionId: EpPropMergeTypeWithNull<string>;
  PhoneNumber: EpPropMergeTypeWithNull<string>;
  Status: EpPropMergeTypeWithNull<number>;
  Type: EpPropMergeTypeWithNull<number>;
  PageInput: {
    PageIndex: number;
    PageSize: number;
  };
}
export interface FlowItem {
  PrescriptionId?: number;
  PrescriptionIdStr?: string;
  TreatOrderNo?: number;
  TreatOrderNoStr?: string;
  FlowId?: string;
  PrescriptionName?: string;
  PatientPhoneNumber?: string;
  PatientUserId?: string;
  PatientName?: string;
  IsSendGoods?: boolean;
  IsTest?: boolean;
  StatusRemark?: any;
  Type?: number;
  Status?: number;
  CreatedTime?: string;
  CreatorId?: any;
  UpdatedTime?: any;
  UpdaterId?: any;
}
export interface FlowInstanceInputDTO {
  PrescriptionId: string;
  PageInput: {
    PageIndex: number;
    PageSize: number;
  };
}
export interface FlowInstanceItem {
  Id: number;
  FlowId: string;
  MainFlowId: string;
  ParentFlowId: string;
  ActivityName: string;
  ActionPoint: string;
  ActionCode: string;
  PatientId: string;
  PrescriptionId: number;
  PrescriptionIdStr: string;
  TreatOrderNo: number;
  TreatOrderNoStr: string;
  Message: string;
  Context: string;
  CreatedTime: string;
  CreatorId?: any;
  UpdatedTime?: any;
  UpdaterId?: any;
  HttpTraceId: string;
  Override: boolean;
  ContextIsEmpty: boolean;
  LockKey: string;
}
