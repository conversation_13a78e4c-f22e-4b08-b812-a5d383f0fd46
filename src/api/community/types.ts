import { type EpPropMergeTypeWithNull } from "element-plus";

export interface CommunityMoItem {
  BaseMoItemId: string;
  BaseMoItemName: string;
  Price: number;
  TotalCount: number;
  ExecuteCount: number;
  RefundCount: number;
  Count: number;
  StrId: string;
}
export interface RefundExecInputDTO {
  RefundCount: number;
  Id: string;
  UserId: string;
  OrderDetailId: string;
  Reason: string;
}
export interface GetExecRecordInputDTO {
  State?: number;
  BaseMoItemId?: EpPropMergeTypeWithNull<string>;
  DctId?: EpPropMergeTypeWithNull<string>;
  OrgId?: EpPropMergeTypeWithNull<string>;
  ExecOrgId?: EpPropMergeTypeWithNull<string>;
  PreId?: EpPropMergeTypeWithNull<string>;
  StarDate: string;
  EndDate: string;
  PageIndex: number;
  PageSize: number;
}
export interface ExecuteRecord {
  Id?: number;
  StrId?: string;
  PreId?: string;
  MoItemId?: number;
  StrMoItemId?: string;
  PreOrgName?: string;
  PreDoctorName?: string;
  MoItemName?: string;
  BaseMoItemId?: string;
  DoctorName?: string;
  PatientId?: string;
  PatientName?: string;
  Age?: number;
  Sex?: string;
  InstrumentId?: any;
  InstrumentName?: any;
  OrgName?: string;
  CreatedTime?: string;
  State?: number;
  OperaTime?: string;
}
