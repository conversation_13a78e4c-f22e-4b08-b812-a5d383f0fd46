import { type TableV2FixedDir, type TableV2SortOrder } from "element-plus";

export type CellRenderProps<T extends Record<string, any>> = {
  rowData: T;
  rowIndex: number;
};

export type HeaderRenderProps<T extends Record<string, any>> = {
  column: TableV2Column<T>;
  columnIndex: number;
};

export type OperationButton<T extends Record<string, any>> = {
  label: string;
  type?: "primary" | "success" | "warning" | "danger" | "info" | "text";
  disabled?: boolean;
  onClick?: (row: T) => void;
};

// 基础列类型（无operations）
type BaseTableV2Column<T extends Record<string, any>> = {
  /** 字段名 */
  key: string & keyof T;
  /** 列标题 */
  title: string;
  /** 列宽度 */
  width: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 最小宽度 */
  minWidth?: number;
  /** 是否固定左右侧 */
  fixed?: TableV2FixedDir;
  /** 是否开启排序 */
  sortable?: boolean;
  /** 初始排序状态，默认降序 */
  sortOrder?: TableV2SortOrder;
  /**
   * 排序事件
   *
   * 未设置，则使用 key 对应的值，根据 sortOrder 排序
   * 如果设置，则使用 sortMethod 排序
   */
  sortMethod?: (order: TableV2SortOrder) => T[];
  /** 自定义 cell 显示内容，不再根据 key 来显示，根据 cellBuilder 返回值来显示 */
  cellBuilder?: (row: T) => string | undefined | null;
  /** 自定义 cell 渲染，需要开启【lang="tsx"】 */
  cellRenderer?: (props: CellRenderProps<T>) => VNode;
  /** 自定义 header 渲染，需要开启【lang="tsx"】 */
  headerCellRenderer?: (props: HeaderRenderProps<T>) => VNode;
};

// 操作列类型（有operations且key必须为"operations"）
export type OperationsTableV2Column<T extends Record<string, any>> = {
  /** 字段名 - 操作列必须为"operations" */
  key: "operations";
  /** 列标题 */
  title: string;
  /** 列宽度 */
  width: number;
  /** 最大宽度 */
  maxWidth?: number;
  /** 最小宽度 */
  minWidth?: number;
  /** 是否固定左右侧 */
  fixed?: TableV2FixedDir;
  /**
   * 操作按钮列
   *
   * 当设置operations时，key必须为"operations"，并按顺序显示操作按钮
   */
  operations: OperationButton<T>[];
  /** 是否开启排序 */
  sortable?: boolean;
  /** 初始排序状态，默认降序 */
  sortOrder?: TableV2SortOrder;
  /**
   * 排序事件
   *
   * 未设置，则使用 key 对应的值，根据 sortOrder 排序
   * 如果设置，则使用 sortMethod 排序
   */
  sortMethod?: (order: TableV2SortOrder) => T[];
  /** 自定义 cell 显示内容，不再根据 key 来显示，根据 cellBuilder 返回值来显示 */
  cellBuilder?: (row: T) => string | undefined | null;
  /** 自定义 cell 渲染，需要开启【lang="tsx"】 */
  cellRenderer?: (props: CellRenderProps<T>) => VNode;
  /** 自定义 header 渲染，需要开启【lang="tsx"】 */
  headerCellRenderer?: (props: HeaderRenderProps<T>) => VNode;
};

/**
 * 表格列配置类型
 *
 * 约束规则：
 * - 当operations有值时，key必须为"operations"
 * - 当operations无值时，key可以是T的任意键
 */
export type TableV2Column<T extends Record<string, any>> =
  | BaseTableV2Column<T>
  | OperationsTableV2Column<T>;
