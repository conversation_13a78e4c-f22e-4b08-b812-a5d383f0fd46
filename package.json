{"name": "platform-operation", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev1": "NODE_ENV=production vite --mode debug", "dev": "pnpm i && vite", "stg": "vite --mode stg", "pro": "vite --mode pro", "build:dev": "vite build", "build:stg": "vite build --mode stg", "build:pro": "vite build --mode pro", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint --fix ./src", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@stomp/stompjs": "^7.0.1", "@types/crypto-js": "^4.2.2", "@types/pinyin": "^2.10.2", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vueuse/core": "^13.0.0", "@wangeditor-next/editor": "^5.6.34", "@wangeditor-next/editor-for-vue": "^5.1.14", "axios": "^1.8.3", "codemirror": "^5.65.18", "codemirror-editor-vue3": "^2.8.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "default-passive-events": "^2.0.0", "echarts": "^5.6.0", "element-plus": "^2.9.6", "exceljs": "^4.4.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.1", "pinyin": "4.0.0-alpha.2", "qrcode.vue": "^3.6.0", "qs": "^6.14.0", "segmentit": "^2.0.3", "sortablejs": "^1.15.6", "vue": "^3.5.13", "vue-i18n": "^11.1.2", "vue-json-pretty": "^2.4.0", "vue-router": "^4.5.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.22.0", "@iconify/utils": "^2.3.0", "@types/codemirror": "^5.60.15", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.10", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.18", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "commitizen": "^4.3.1", "cz-git": "^1.11.1", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.33.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "sass": "^1.86.0", "stylelint": "^16.16.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^37.0.0", "terser": "^5.39.0", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1", "unocss": "65.4.3", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.2", "vite-plugin-mock-dev-server": "^1.8.4", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.2.8"}, "engines": {"node": ">=18.0.0"}, "author": "成都康复行网络科技有限公司", "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a"}